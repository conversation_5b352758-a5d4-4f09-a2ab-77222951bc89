@import 'tailwindcss';
@import './button.css';
@reference "./flexywind.css";
@source "../views";

@plugin "@iconify/tailwind4" {
  prefixes: hugeicons;
  scale: 1.2;
}



html,
body {
  font-family: 'Inter', sans-serif;
}


@theme {
 /* --color-primary-50: oklch(0.987 0.026 102.212);
  --color-primary-100: oklch(0.973 0.071 103.193);
  --color-primary-200: oklch(0.945 0.129 101.54);
  --color-primary-300: oklch(0.905 0.182 98.111);
  --color-primary-400: oklch(0.852 0.199 91.936);
  --color-primary-500: oklch(0.795 0.184 86.047);
  --color-primary-600: oklch(0.681 0.162 75.834);
  --color-primary-700: oklch(0.554 0.135 66.442);
  --color-primary-800: oklch(0.476 0.114 61.907);
  --color-primary-900: oklch(0.421 0.095 57.708);
  --color-primary-950: oklch(0.286 0.066 53.813); */

  --color-primary-50: #fefbec;
  --color-primary-100: #fcf1c9;
  --color-primary-200: #f8e18f;
  --color-primary-300: #f4c846;
  --color-primary-400: #f2b72d;
  --color-primary-500: #eb9815;
  --color-primary-600: #d0720f;
  --color-primary-700: #ad5110;
  --color-primary-800: #8d3f13;
  --color-primary-900: #743413;
  --color-primary-950: #421a06;




  --font-display: 'Playfair', serif;
  --font-body: 'Inter', sans-serif;

  --color-fg: var(--color-gray-700);
  --color-fg-title: var(--color-gray-900);
  --color-fg-muted: var(--color-gray-600);

  --color-bg: var(--color-white);
  --color-bg-muted: var(--color-gray-100);
  --color-bg-subtle: var(--color-gray-50);
  --color-bg-surface: var(--color-gray-200);
}