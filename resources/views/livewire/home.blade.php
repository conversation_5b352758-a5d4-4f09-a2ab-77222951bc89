<?php

use Livewire\Volt\Component;

new class extends Component {
    public $title = 'Columna - Responsible Business Consulting | Home';
    public $description = 'Columna offers expert business consulting services focusing on strategic advisory, ethical leadership development, and governance excellence. Transform your organization with our sustainable solutions.';
    public $keywords = 'business consulting, ethical leadership, governance excellence, strategic advisory, ESG consulting, risk management';
    public $ogImage = '/hero_1.webp';
    public $schemaType = 'Organization';
    public $schemaName = 'Columna Consulting';
    public $schemaDescription = 'Leading business consulting firm specializing in ethical leadership, governance excellence, and strategic advisory services.';
    public $schemaAdditionalData = [
        'mainEntityOfPage' => [
            '@type' => 'WebPage',
            '@id' => 'https://columna.com',
        ],
        'offers' => [
            '@type' => 'Offer',
            'name' => 'Business Consulting Services',
            'description' => 'Strategic advisory, ethical leadership development, and governance excellence services',
        ],
    ];
}; ?>

<main>
    <x-organisms.hero />
    <x-organisms.home-features />
    <x-organisms.intro-about />
    <x-organisms.different-nd-advantages />
    <x-organisms.cta />
</main>
