@props(['title', 'activePage'])
<section class="pt-6">
    <x-atoms.container nopadding
        class="bg-primary-950 ui-card [--card-padding:calc(var(--spacing)*1.5)] [--card-radius:var(--radius-xl)] overflow-hidden relative">
        <div
            class="absolute inset-0 text-primary-600 opacity-20
            bg-[radial-gradient(currentColor_1px,transparent_1px)] [background-size:16px_16px] 
            [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,#000_70%,transparent_100%)]">
        </div>
        <div class="p-5 sm:p-8 lg:p-12 flex flex-col items-center justify-center text-center relative">


            <nav class="flex px-5 py-3 bg-primary-100/30 text-primary-100 rounded-lg "
                aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                    <li class="inline-flex items-center">
                        <a href="/" aria-label="Lien vers la page principale"
                            class="inline-flex items-center text-sm font-medium hover:text-primary-200">
                            <span class="iconify hugeicons--home-01 me-2.5"></span>
                            Accueil
                        </a>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                             <span class="iconify mx-1 hugeicons--arrow-right-01"></span>
                            <span
                                class="ms-1 text-sm font-medium text-primary-300">
                            {{ $activePage }}
                            </span>
                        </div>
                    </li>
                </ol>
            </nav>

            <h1
                class="text-3xl min-[390px]:text-4xl sm:text-5xl md:text-6xl sm:text-balance mt-4 font-display text-white">
                {{ $title }}
            </h1>
            @if(isset($description))
                {{ $description }}
            @endif
        </div>
    </x-atoms.container>
</section>
