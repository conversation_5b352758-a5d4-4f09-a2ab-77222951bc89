@props(['subtext', 'title', 'wrapperClass' => '', 'noMaxW' => false, 'center' => true, 'description' => ''])
@php
    $containerClass =
        'flex flex-col ' .
        ($center ? 'text-center mx-auto items-center ' : '') .
        (!$noMaxW ? 'max-w-2xl  ' : '') .
        $wrapperClass;
@endphp

<div class="{{ $containerClass }}">
    <span
        class="w-max bg-primary-900 border border-primary-600/40 text-primary-100 rounded-lg px-3 py-1 text-sm flex items-center">
        <span class="flex mr-2">
            <span class="iconify hugeicons--target-01"></span>
        </span>
        <span>
            {{ $subtext }}
        </span>
    </span>
    <h2 class="text-3xl text-balance sm:text-4xl md:text-5xl font-display text-fg-title mt-5">
        {{ $title }}
    </h2>
    @if (isset($description))
        <p class="mt-6 max-w-xl text-fg-muted">
            {{ $description }}
        </p>
    @endif
</div>
