@php
    $items = [
        [
            'id' => '01',
            'title' => 'Des services à forte valeur émotionnelle',
            'icon' => 'hugeicons--in-love',
            'description' => 'Cadeaux, formations, ateliers et événements pensés pour toucher, inspirer et engager.',
        ],
        [
            'id' => '02',
            'title' => 'Une transformation des gestes quotidiens',
            'icon' => 'hugeicons--tree-02',
            'description' => 'Chaque interaction devient un levier de relation authentique et durable.',
        ],
        [
            'id' => '03',
            'title' => 'Un accompagnement stratégique et sensible',
            'description' => 'Nous allions sens, esthétique et efficacité pour des actions qui marquent.',
            'icon' => 'hugeicons--gift',
        ],
        [
            'id' => '04',
            'title' => 'Une expertise rare et transversale',
            'description' => 'À la croisée du bien-être, du marketing émotionnel et de la culture de service.',
            'icon' => 'hugeicons--gift',
        ],
        [
            'id' => '05',
            'title' => 'Un engagement sociétal concret',
            'description' =>
                'Avec notre branche solidaire Effet Surprise, nous apportons la joie à ceux qu’on oublie trop souvent.',
            'icon' => 'hugeicons--target-dollar',
        ],
    ];
@endphp

<section class="mt-24 lg:mt-28">
    <x-blocks.section-title :noMaxW="true" wrapperClass="max-w-3xl" title="Une approche unique, à la croisée de l’humain et de l’impact"
        subtext="Ce que nous apportons vraiment" />
    <x-atoms.container class="mt-16 grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
        @foreach ($items as $item)
            <div class="flec flex-col p-5 sm:p-8 bg-bg/50 backdrop-blur-xs border border-bg-surface rounded-lg">
                <div class="flex items-start justify-between">
                    <span
                        class="p-2 border border-bg-surface/70 bg-primary-300 text-primary-900 rounded-md d-flex-place-center">
                        <span class="flex text-xl iconify {{ $item['icon'] }}"></span>
                    </span>
                    <div class="">
                        {{ $item['id'] }}
                    </div>
                </div>
                <h3 class="font-semibold text-lg text-fg-title mt-10">
                    {{ $item['title'] }}
                </h3>
                <p class="mt-4 text-fg-muted">
                    {{ $item['description'] }}
                </p>
            </div>
        @endforeach
    </x-atoms.container>
</section>
