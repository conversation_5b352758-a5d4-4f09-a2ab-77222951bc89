@php
    $stats = [
        [
            'value' => '1000',
            'label' => 'Cadeaux distribués',
        ],
        [
            'value' => '500',
            'label' => 'Personnes aidées',
        ],
        [
            'value' => '200',
            'label' => 'Projets réalisés',
        ],
        [
            'value' => '10',
            'label' => "Années d'expérience",
        ],
    ];
@endphp

<section class="mt-24 lg:mt-28 xl:mt-32">
    <x-atoms.container class="flex flex-col md:flex-row gap-16">
        <div class="md:w-1/2 lg:pr-10 xl:pr-16 relative">
            <img src="/happy_woman_african.webp" alt="event image" class="w-full h-full object-cover rounded-lg">
        </div>
        <div class="flex-1 flex flex-col md:py-5">
            <x-blocks.section-title :center="false" subtext="A propos" title="Qui nous sommes" />
            <p class="text-fg-muted mt-7">
                Land of Joy est une entreprise engagée dans l’humanisation des relations professionnelles et sociales.
            </p>
            <p class="text-fg-muted mt-2">
                Nous concevons des expériences, des services et des événements qui remettent la joie,
                l’attention et la relation authentique au cœur des interactions humaines – en entreprise
                comme dans la vie quotidienne.
            </p>
            <div class="mt-8 flex">
                <a href="/a-propos" class="btn btn-solid btn-solid-primary text-primary-900 rounded-lg btn-md">
                    En savoir plus
                </a>
            </div>
        </div>
    </x-atoms.container>
    <x-atoms.container>
        <div class="mt-10 grid sm:grid-cols-2 lg:grid-cols-4 gap-5 p-5 sm:p-8 bg-primary-950 rounded-lg divide-y sm:divide-x lg:divide-y-0 divide-primary-900/50">
        @foreach ($stats as $stat)
            <div class="flex flex-col items-center">
                <span class="text-4xl font-bold text-white">
                    {{ $stat['value'] }}
                </span>
                <span class="text-primary-100 text-sm">
                    {{ $stat['label'] }}
                </span>
            </div>
        @endforeach
    </div>
    </x-atoms.container>
</section>
