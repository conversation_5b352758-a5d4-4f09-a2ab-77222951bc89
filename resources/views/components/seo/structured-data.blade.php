@props([
    'type' => 'Organization',
    'name' => 'Columna Consulting',
    'description' => 'Columna provides strategic advisory, ethical leadership development, and governance excellence services.',
    'url' => null,
    'logo' => '/logo_def.png',
    'additionalData' => []
])

<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "{{ $type }}",
    "name": "{{ $name }}",
    "description": "{{ $description }}",
    "url": "{{ $url ?? url()->current() }}",
    "logo": "{{ url($logo) }}",
    "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "email": "<EMAIL>"
    },
    "sameAs": [
        "#",
        "#",
        "#"
    ],
    @if(!empty($additionalData))
        @foreach($additionalData as $key => $value)
            "{{ $key }}": {!! json_encode($value) !!}@if(!$loop->last),@endif
        @endforeach
    @endif
}
</script>