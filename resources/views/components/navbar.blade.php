<header class="px-1 sm:px-2 lg:px-0 sticky top-0 lg:relative z-50">
    <nav
        class="flex items-center justiby-between py-2 max-w-7xl mx-auto w-full px-4 sm:px-10 lg:px-5 bg-white border border-bg-surface/50 shadow-sm shadow-gray-100/50 lg:shadow-none ui-card [--card-padding:calc(var(--spacing)_*_0.5)] [--card-radius:var(--radius-lg)] lg:bg-transparent lg:border-0">
        <div class="flex-1 flex">
            <a href="/" aria-label="Aller à la page d'accueil" class="">
                <img src="/logo.png" alt="Logo land of joy" width="80" height="80" class="size-14">
            </a>
        </div>
        <x-blocks.nav-items />
        <div class="flex-1 flex items-center justify-end gap-1.5">
            <a href="tel:+" class="btn btn-sm h-9 btn-ghost btn-ghost-gray max-sm:hidden">
                <span class="flex iconify hugeicons--call-02"></span>
                + 243 97 xx xx xxx
            </a>
            <a href="#" class="text-nowrap btn btn-sm sm:h-9 btn-solid btn-solid-neutral text-bg rounded-lg">
                Prendre contact
            </a>
            <div class="flex lg:hidden pl-1 border-l border-gray-200">
                <button data-nav-trigger data-toggle-nav="mainNavbar"
                    class="p-2 rounded-full flex flex-col relative justify-center items-center group"
                    aria-label="Toggle navbar">
                    <span id="line-1"
                        class="w-6 h-0.5 rounded-full bg-gray-600 transition-transform duration-300 ease-linear group-aria-expanded:translate-y-1.5 group-aria-expanded:rotate-[40deg] group-aria-expanded:bg-white"></span>
                    <span id="line-2"
                        class="w-6 origin-center  mt-1 h-0.5 rounded-full bg-gray-600 transition-all duration-300 ease-linear group-aria-expanded:scale-x-0 group-aria-expanded:opacity-0 group-aria-expanded:bg-white"></span>
                    <span id="line-3"
                        class="w-6 mt-1 h-0.5 rounded-full bg-gray-600 transition-all duration-300 ease-linear group-aria-expanded:-translate-y-1.5 group-aria-expanded:-rotate-[40deg] group-aria-expanded:bg-white"></span>
                </button>
            </div>
        </div>
    </nav>
</header>
