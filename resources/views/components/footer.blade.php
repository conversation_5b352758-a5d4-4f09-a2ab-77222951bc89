@php
    $navItems = [
        [
            'label' => 'Accueil',
            'link' => '/',
        ],
        [
            'label' => 'A propos',
            'link' => '/a-propos',
        ],
        [
            'label' => 'Services',
            'link' => '/services',
        ],
        [
            'label' => 'Contact',
            'link' => '/contact',
        ],
    ];

    $socialLinks = [
         [
            'label' => 'Facebook',
            'link' => '#',
        ],
        [
            'label' => 'Twitter',
            'link' => '#',
        ],
        [
            'label' => 'Instagram',
            'link' => '#',
        ],
    ];

    $contacts = [
        [
            'icon'=>'hugeicons--location-01',
            'label' => 'Adresse',
            'value' => '123 Rue Principale, Ville, Pays',
            'href'=>'#'
        ],
        [
            'icon'=>'hugeicons--mail-01',
            'label' => 'Email',
            'value' => '<EMAIL>',
            'href'=>'mailto:'
        ],
        [
            'icon'=>'hugeicons--call-02',
            'label' => 'Téléphone',
            'value' => '+****************',
            'href'=>'tel:+'
        ],
    ];
@endphp

<footer class="mt-8 pt-8 pb-2 border-t border-bg-surface">
    <x-atoms.container class="">
        <div class="md:flex md:justify-between">
            <div class="mb-6 md:mb-0">
                <a href="/" class="flex items-center">
                    <img src="/logo.png" class="size-10" alt="Land of joy icon" width="100" height="100" />
                    <span class="text-primary-900 self-center text-2xl font-semibold whitespace-nowrap">Land Of Joy</span>
                </a>
            </div>
            <div class="grid grid-cols-2 gap-8 sm:gap-6 sm:grid-cols-3">
                <div>
                    <h2 class="mb-6 text-sm font-semibold text-fg-title uppercase">Navigations</h2>
                    <ul class="text-fg-muted font-medium space-y-4">
                       @foreach ($navItems as $item)
                        <li class="mb-4">
                            <a href="{{ $item['link'] }}" class="hover:text-primary-600 hover:underline ">{{ $item['label'] }}</a>
                        </li>
                       @endforeach
                    </ul>
                </div>
                <div>
                    <h2 class="mb-6 text-sm font-semibold text-fg-title uppercase">Nous suivre</h2>
                    <ul class="text-fg-muted font-medium space-y-4">
                        @foreach ($socialLinks as $item)
                        <li>
                            <a href="{{ $item['link'] }}" class="hover:text-primary-600 hover:underline ">{{ $item['label'] }}</a>
                        </li>
                       @endforeach
                    </ul>
                </div>
                <div>
                    <h2 class="mb-6 text-sm font-semibold text-fg-title uppercase">Contacts</h2>
                    <ul class="text-fg-muted font-medium space-y-4">
                         @foreach ($contacts as $item)
                        <li>
                            <a href="{{ $item['href'] }}" class="hover:text-primary-600 hover:underline ">{{ $item['value'] }}</a>
                        </li>
                       @endforeach
                    </ul>
                </div>
            </div>
        </div>
        <hr class="my-6 border-gray-200 sm:mx-auto" />
        <div class="text-center flex justify-center">
            <span class="text-sm text-gfg-muted">©  {{ date('Y') }} LandOfJoy. Tout droits réservés.
            </span>
        </div>
    </x-atoms.container>
</footer>
